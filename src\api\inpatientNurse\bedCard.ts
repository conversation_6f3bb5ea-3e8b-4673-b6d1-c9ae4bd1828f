import { useBedInfoApi } from '/@/api/shared/bedInfo';
import { useInpatientRegisterApi } from '/@/api/inpatient/inpatientRegister';
import { useBasicInfoApi } from '/@/api/shared/basicInfo';

// 床位卡数据类型定义
export interface BedCardInfo {
	// 床位基础信息
	id: number;
	bedNo: string;
	deptId: number;
	deptName: string;
	wardId: number;
	wardName: string;
	roomNo?: string;
	bedType?: string;
	bedStatus?: string;
	bedLevelName?: string;
	orderNo?: number;
	status: number;

	// 患者信息（如果有住院患者）
	patientInfo?: {
		patientId: number;
		patientName: string;
		patientNo: string;
		inpatientNo: string;
		inpatientSerialNo: string;
		sex: string;
		age: number;
		idCardNo: string;
		contactPhone: string;
		inpatientTime: string;
		inpatientDays: number;
		doctorName?: string;
		diagnosis?: string;
		nursingLevel?: string;
	};

	// 床位状态标识
	isOccupied: boolean;
	isEmergency: boolean;
	isIsolation: boolean;
	isContaminated: boolean;
}

// 床位卡查询参数
export interface BedCardQueryParams {
	deptId?: number;
	wardId?: number;
	bedStatus?: string;
	bedType?: string;
	patientName?: string;
	isOccupied?: boolean;
	page?: number;
	pageSize?: number;
}

// 床位统计信息
export interface BedStatistics {
	totalBeds: number;
	occupiedBeds: number;
	availableBeds: number;
	contaminatedBeds: number;
	isolationBeds: number;
	occupancyRate: number;
}

// 床位卡管理服务
export const useBedCardApi = () => {
	const bedInfoApi = useBedInfoApi();
	const inpatientRegisterApi = useInpatientRegisterApi();
	const basicInfoApi = useBasicInfoApi();

	/**
	 * 获取床位卡列表
	 */
	const getBedCards = async (params: BedCardQueryParams = {}): Promise<BedCardInfo[]> => {
		try {
			// 1. 获取床位基础信息
			const bedInfoParams = {
				page: params.page || 1,
				pageSize: params.pageSize || 100,
				deptId: params.deptId,
				wardId: params.wardId,
				bedStatus: params.bedStatus,
				bedType: params.bedType,
			};

			const bedInfoResult = await bedInfoApi.page(bedInfoParams);
			const bedList = bedInfoResult.data.result?.items || [];

			// 2. 获取住院登记信息
			const inpatientParams = {
				page: 1,
				pageSize: 1000,
				status: 0, // 在院状态
			};

			const inpatientResult = await inpatientRegisterApi.page(inpatientParams);
			const inpatientList = inpatientResult.data.result?.items || [];

			// 3. 数据整合：将床位信息和患者信息关联
			const bedCards: BedCardInfo[] = bedList.map((bed: any) => {
				// 查找该床位对应的住院患者
				const patient = inpatientList.find((p: any) => p.deptId === bed.deptId && p.wardId === bed.wardId && p.bedNo === bed.bedNo);

				// 计算住院天数
				const inpatientDays = patient ? Math.ceil((new Date().getTime() - new Date(patient.inpatientTime).getTime()) / (1000 * 60 * 60 * 24)) : 0;

				const bedCard: BedCardInfo = {
					id: bed.id,
					bedNo: bed.bedNo,
					deptId: bed.deptId,
					deptName: bed.deptName,
					wardId: bed.wardId,
					wardName: bed.wardName,
					roomNo: bed.roomNo,
					bedType: bed.bedType,
					bedStatus: bed.bedStatus,
					bedLevelName: bed.bedLevelFkDisplayName,
					orderNo: bed.orderNo,
					status: bed.status,

					// 患者信息
					patientInfo: patient
						? {
								patientId: patient.patientId,
								patientName: patient.patientName,
								patientNo: patient.patientNo,
								inpatientNo: patient.inpatientNo,
								inpatientSerialNo: patient.inpatientSerialNo,
								sex: patient.sex,
								age: patient.age,
								idCardNo: patient.idCardNo,
								contactPhone: patient.contactPhone,
								inpatientTime: patient.inpatientTime,
								inpatientDays: inpatientDays,
								doctorName: patient.doctorName,
								diagnosis: patient.diagnosis,
								nursingLevel: patient.nursingLevel,
							}
						: undefined,

					// 状态标识
					isOccupied: !!patient,
					isEmergency: patient?.isEmergency || false,
					isIsolation: bed.bedStatus === 'Q',
					isContaminated: bed.bedStatus === 'K',
				};

				return bedCard;
			});

			// 4. 根据查询条件过滤
			let filteredBedCards = bedCards;

			if (params.patientName) {
				filteredBedCards = filteredBedCards.filter((bed) => bed.patientInfo?.patientName?.includes(params.patientName!));
			}

			if (params.isOccupied !== undefined) {
				filteredBedCards = filteredBedCards.filter((bed) => bed.isOccupied === params.isOccupied);
			}

			return filteredBedCards;
		} catch (error) {
			console.error('获取床位卡数据失败:', error);
			throw error;
		}
	};

	/**
	 * 获取床位统计信息
	 */
	const getBedStatistics = async (deptId?: number, wardId?: number): Promise<BedStatistics> => {
		try {
			const bedCards = await getBedCards({ deptId, wardId, pageSize: 1000 });

			const totalBeds = bedCards.length;
			const occupiedBeds = bedCards.filter((bed) => bed.isOccupied).length;
			const availableBeds = bedCards.filter((bed) => !bed.isOccupied && bed.bedStatus === 'N').length;
			const contaminatedBeds = bedCards.filter((bed) => bed.isContaminated).length;
			const isolationBeds = bedCards.filter((bed) => bed.isIsolation).length;
			const occupancyRate = totalBeds > 0 ? Math.round((occupiedBeds / totalBeds) * 100) : 0;

			return {
				totalBeds,
				occupiedBeds,
				availableBeds,
				contaminatedBeds,
				isolationBeds,
				occupancyRate,
			};
		} catch (error) {
			console.error('获取床位统计信息失败:', error);
			throw error;
		}
	};

	/**
	 * 更新床位状态
	 */
	const updateBedStatus = async (bedId: number, bedStatus: string) => {
		try {
			const bedInfo = await bedInfoApi.detail({ id: bedId });
			const updatedBed = {
				...bedInfo.data.result,
				bedStatus: bedStatus,
			};

			return await bedInfoApi.update(updatedBed);
		} catch (error) {
			console.error('更新床位状态失败:', error);
			throw error;
		}
	};

	/**
	 * 获取科室列表
	 */
	const getDepartments = async () => {
		try {
			return await basicInfoApi.getInpatientDepartments({});
		} catch (error) {
			console.error('获取科室列表失败:', error);
			throw error;
		}
	};

	/**
	 * 获取病区列表
	 */
	const getWards = async (deptId: number) => {
		try {
			return await basicInfoApi.getInpatientWards({ parentId: deptId });
		} catch (error) {
			console.error('获取病区列表失败:', error);
			throw error;
		}
	};

	return {
		getBedCards,
		getBedStatistics,
		updateBedStatus,
		getDepartments,
		getWards,
	};
};
