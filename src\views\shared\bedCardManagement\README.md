# 床位卡管理功能

## 功能概述

床位卡管理是一个可视化的床位状态展示和管理界面，主要用于护士站或病区管理。提供了直观的床位卡片视图和列表视图，方便医护人员快速了解床位使用情况。

## 主要功能

### 1. 床位状态可视化
- **卡片视图**：以卡片形式展示每个床位的详细信息
- **列表视图**：以表格形式展示床位信息，便于批量操作
- **状态标识**：不同颜色标识床位状态（空床、占床、污染、隔离等）

### 2. 患者信息展示
- 患者基本信息（姓名、性别、年龄）
- 住院信息（住院号、住院天数）
- 医疗信息（主治医师、诊断、护理等级）

### 3. 筛选和搜索
- **科室筛选**：按科室过滤床位
- **病区筛选**：按病区过滤床位
- **状态筛选**：按床位状态过滤
- **类型筛选**：按床位类型过滤
- **患者搜索**：按患者姓名搜索
- **占用状态**：筛选有患者/无患者的床位

### 4. 统计信息
- 总床位数
- 占用床位数
- 空闲床位数
- 床位使用率

### 5. 快速操作
- 床位状态更改
- 入院操作（预留接口）
- 出院操作（预留接口）
- 转床操作（预留接口）

## 技术实现

### 数据整合
- 利用现有的 `bedInfo` API 获取床位基础信息
- 利用现有的 `inpatientRegister` API 获取住院患者信息
- 在前端进行数据关联和整合

### 组件结构
```
bedCardManagement/
├── index.vue                 # 主页面
├── README.md                 # 说明文档
└── components/
    └── BedCard.vue          # 床位卡组件
```

### API接口
```typescript
// 床位卡数据整合服务
useBedCardApi() {
  getBedCards()           // 获取床位卡列表
  getBedStatistics()      // 获取床位统计信息
  updateBedStatus()       // 更新床位状态
  getDepartments()        // 获取科室列表
  getWards()             // 获取病区列表
}
```

## 访问方式

### 路由配置
系统已配置以下路由：

1. **住院管理模块下**：
   - 路径：`/inpatient/bedCardManagement`
   - 名称：`bedCardManagement`

2. **系统基础模块下**：
   - 路径：`/shared/bedCardManagement`
   - 名称：`sharedBedCardManagement`

### 直接访问
可以通过以下URL直接访问：
- `http://your-domain/#/inpatient/bedCardManagement`
- `http://your-domain/#/shared/bedCardManagement`

## 使用说明

### 1. 筛选操作
1. 选择科室：从下拉列表中选择要查看的科室
2. 选择病区：科室选择后，病区列表会自动更新
3. 选择状态：可按床位状态进行筛选
4. 搜索患者：输入患者姓名进行搜索
5. 点击"查询"按钮执行筛选

### 2. 视图切换
- 点击右上角的"网格"/"列表"按钮切换视图模式
- 网格视图：适合快速浏览床位状态
- 列表视图：适合查看详细信息和批量操作

### 3. 床位操作
- **状态更改**：点击"状态"按钮可更改床位状态
- **入院/出院**：点击相应按钮（功能预留，需要后续开发）
- **转床**：点击"转床"按钮（功能预留，需要后续开发）

### 4. 数据刷新
- 点击"刷新"按钮更新床位数据
- 统计信息卡片右上角有独立的刷新按钮

## 响应式设计

系统支持多种设备：
- **桌面端**：完整功能展示
- **平板端**：自适应布局
- **手机端**：优化的移动端界面

## 状态说明

### 床位状态
- **空床 (N)**：绿色标识，可安排入院
- **占床 (O)**：蓝色标识，有患者住院
- **污染 (K)**：红色标识，需要清洁处理
- **隔离 (Q)**：橙色标识，隔离病房
- **关闭 (C)**：灰色标识，暂停使用

### 特殊标识
- **急诊**：红色"急"字标签
- **隔离**：橙色"隔离"标签
- **污染**：红色"污染"标签

## 注意事项

1. **权限控制**：部分操作可能需要相应的权限
2. **数据实时性**：建议定期刷新数据以获取最新状态
3. **网络依赖**：功能依赖网络连接，请确保网络畅通
4. **浏览器兼容**：建议使用现代浏览器以获得最佳体验

## 后续扩展

### 计划功能
- 实时数据推送
- 床位预约管理
- 床位使用历史
- 报表统计功能
- 移动端APP支持

### 集成建议
- 与护士站系统集成
- 与医生工作站集成
- 与医院信息系统深度整合
