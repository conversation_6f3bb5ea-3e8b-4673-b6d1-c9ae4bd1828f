<template>
	<div class="bed-card-management">
		<!-- 筛选工具栏 -->
		<el-card shadow="hover" class="filter-card">
			<el-form :model="queryParams" inline label-width="80px">
				<el-form-item label="科室">
					<el-select v-model="queryParams.deptId" placeholder="请选择科室" clearable filterable @change="handleDeptChange" style="width: 200px">
						<el-option v-for="dept in deptList" :key="dept.id" :label="dept.name" :value="dept.id" />
					</el-select>
				</el-form-item>

				<el-form-item label="病区">
					<el-select v-model="queryParams.wardId" placeholder="请选择病区" clearable filterable style="width: 200px">
						<el-option v-for="ward in wardList" :key="ward.id" :label="ward.name" :value="ward.id" />
					</el-select>
				</el-form-item>

				<el-form-item label="床位状态">
					<el-select v-model="queryParams.bedStatus" placeholder="请选择状态" clearable style="width: 150px">
						<el-option label="全部" value="" />
						<el-option label="空床" value="N" />
						<el-option label="占床" value="O" />
						<el-option label="污染" value="K" />
						<el-option label="隔离" value="Q" />
						<el-option label="关闭" value="C" />
					</el-select>
				</el-form-item>

				<el-form-item label="床位类型">
					<g-sys-dict v-model="queryParams.bedType" code="InpatientBedType" render-as="select" placeholder="请选择床位类型" clearable filterable style="width: 150px" />
				</el-form-item>

				<el-form-item label="患者姓名">
					<el-input v-model="queryParams.patientName" placeholder="请输入患者姓名" clearable style="width: 150px" />
				</el-form-item>

				<el-form-item label="占用状态">
					<el-select v-model="queryParams.isOccupied" placeholder="请选择" clearable style="width: 120px">
						<el-option label="全部" :value="undefined" />
						<el-option label="有患者" :value="true" />
						<el-option label="无患者" :value="false" />
					</el-select>
				</el-form-item>

				<el-form-item>
					<el-button type="primary" icon="ele-Search" @click="handleQuery" :loading="loading"> 查询 </el-button>
					<el-button icon="ele-Refresh" @click="handleReset"> 重置 </el-button>
					<el-button icon="ele-RefreshRight" @click="handleRefresh" :loading="loading"> 刷新 </el-button>
				</el-form-item>
			</el-form>
		</el-card>

		<!-- 统计信息卡片 -->
		<el-card shadow="hover" class="statistics-card">
			<template #header>
				<div class="card-header">
					<span>床位统计</span>
					<el-button type="text" @click="handleRefreshStats" :loading="statsLoading">
						<el-icon><Refresh /></el-icon>
					</el-button>
				</div>
			</template>

			<el-row :gutter="20">
				<el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
					<div class="stat-item total">
						<div class="stat-number">{{ statistics.totalBeds }}</div>
						<div class="stat-label">总床位</div>
					</div>
				</el-col>
				<el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
					<div class="stat-item occupied">
						<div class="stat-number">{{ statistics.occupiedBeds }}</div>
						<div class="stat-label">占用床位</div>
					</div>
				</el-col>
				<el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
					<div class="stat-item available">
						<div class="stat-number">{{ statistics.availableBeds }}</div>
						<div class="stat-label">空闲床位</div>
					</div>
				</el-col>
				<el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
					<div class="stat-item rate">
						<div class="stat-number">{{ statistics.occupancyRate }}%</div>
						<div class="stat-label">床位使用率</div>
					</div>
				</el-col>
			</el-row>
		</el-card>

		<!-- 床位卡网格 -->
		<el-card shadow="hover" class="bed-grid-card">
			<template #header>
				<div class="card-header">
					<span>床位一览 ({{ bedCards.length }})</span>
					<div class="header-actions">
						<el-radio-group v-model="viewMode" size="small">
							<el-radio-button label="grid">网格</el-radio-button>
							<el-radio-button label="list">列表</el-radio-button>
						</el-radio-group>
					</div>
				</div>
			</template>

			<div v-loading="loading" class="bed-container">
				<!-- 网格视图 -->
				<div v-if="viewMode === 'grid'" class="bed-grid">
					<BedCard v-for="bed in bedCards" :key="bed.id" :bed-info="bed" @admission="handleAdmission" @discharge="handleDischarge" @transfer="handleTransfer" @status-change="handleStatusChange" />
				</div>

				<!-- 列表视图 -->
				<div v-else class="bed-list">
					<el-table :data="bedCards" stripe>
						<el-table-column prop="bedNo" label="床位号" width="80" />
						<el-table-column prop="deptName" label="科室" width="120" />
						<el-table-column prop="wardName" label="病区" width="120" />
						<el-table-column prop="roomNo" label="房间" width="80" />
						<el-table-column label="床位状态" width="100">
							<template #default="scope">
								<el-tag :type="getStatusTagType(scope.row.bedStatus)" size="small">
									{{ getStatusText(scope.row.bedStatus) }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column label="患者信息" min-width="200">
							<template #default="scope">
								<div v-if="scope.row.isOccupied && scope.row.patientInfo">
									<div>{{ scope.row.patientInfo.patientName }}</div>
									<div class="text-sm text-gray-500">{{ scope.row.patientInfo.inpatientNo }} | {{ getGenderText(scope.row.patientInfo.sex) }} | {{ scope.row.patientInfo.age }}岁</div>
								</div>
								<span v-else class="text-gray-400">空床</span>
							</template>
						</el-table-column>
						<el-table-column label="住院天数" width="100">
							<template #default="scope">
								<span v-if="scope.row.patientInfo"> {{ scope.row.patientInfo.inpatientDays }}天 </span>
								<span v-else>-</span>
							</template>
						</el-table-column>
						<el-table-column label="操作" width="200" fixed="right">
							<template #default="scope">
								<el-button-group size="small">
									<el-button v-if="!scope.row.isOccupied" type="primary" @click="handleAdmission(scope.row)" :disabled="scope.row.bedStatus !== 'N'"> 入院 </el-button>
									<el-button v-if="scope.row.isOccupied" type="warning" @click="handleDischarge(scope.row)"> 出院 </el-button>
									<el-button @click="handleTransfer(scope.row)">转床</el-button>
									<el-button @click="handleStatusChange(scope.row)">状态</el-button>
								</el-button-group>
							</template>
						</el-table-column>
					</el-table>
				</div>

				<!-- 空状态 -->
				<el-empty v-if="!loading && bedCards.length === 0" description="暂无床位数据" />
			</div>
		</el-card>
	</div>
</template>

<script setup lang="ts" name="bedCardManagement">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue';
import BedCard from './component/bedCard.vue';
import { useBedCardApi, type BedCardInfo, type BedCardQueryParams, type BedStatistics } from '../../../api/inpatientNurse/bedCard';

// API实例
const bedCardApi = useBedCardApi();

// 响应式数据
const loading = ref(false);
const statsLoading = ref(false);
const viewMode = ref<'grid' | 'list'>('grid');

// 查询参数
const queryParams = reactive<BedCardQueryParams>({
	deptId: undefined,
	wardId: undefined,
	bedStatus: '',
	bedType: '',
	patientName: '',
	isOccupied: undefined,
});

// 数据列表
const bedCards = ref<BedCardInfo[]>([]);
const deptList = ref<any[]>([]);
const wardList = ref<any[]>([]);

// 统计信息
const statistics = ref<BedStatistics>({
	totalBeds: 0,
	occupiedBeds: 0,
	availableBeds: 0,
	contaminatedBeds: 0,
	isolationBeds: 0,
	occupancyRate: 0,
});

// 页面加载
onMounted(async () => {
	await loadDepartments();
	await handleQuery();
});

// 加载科室列表
const loadDepartments = async () => {
	try {
		const result = await bedCardApi.getDepartments();
		deptList.value = result.data.result || [];
	} catch (error) {
		console.error('加载科室列表失败:', error);
	}
};

// 科室变更事件
const handleDeptChange = async (deptId: number) => {
	queryParams.wardId = undefined;
	wardList.value = [];

	if (deptId) {
		try {
			const result = await bedCardApi.getWards(deptId);
			wardList.value = result.data.result || [];
		} catch (error) {
			console.error('加载病区列表失败:', error);
		}
	}
};

// 查询床位卡数据
const handleQuery = async () => {
	loading.value = true;
	try {
		const [bedCardsResult, statisticsResult] = await Promise.all([bedCardApi.getBedCards(queryParams), bedCardApi.getBedStatistics(queryParams.deptId, queryParams.wardId)]);

		bedCards.value = bedCardsResult;
		statistics.value = statisticsResult;
	} catch (error) {
		console.error('查询床位卡数据失败:', error);
		ElMessage.error('查询床位卡数据失败');
	} finally {
		loading.value = false;
	}
};

// 重置查询条件
const handleReset = () => {
	Object.assign(queryParams, {
		deptId: undefined,
		wardId: undefined,
		bedStatus: '',
		bedType: '',
		patientName: '',
		isOccupied: undefined,
	});
	wardList.value = [];
	handleQuery();
};

// 刷新数据
const handleRefresh = () => {
	handleQuery();
};

// 刷新统计信息
const handleRefreshStats = async () => {
	statsLoading.value = true;
	try {
		const result = await bedCardApi.getBedStatistics(queryParams.deptId, queryParams.wardId);
		statistics.value = result;
	} catch (error) {
		console.error('刷新统计信息失败:', error);
		ElMessage.error('刷新统计信息失败');
	} finally {
		statsLoading.value = false;
	}
};

// 床位操作事件处理
const handleAdmission = (bedInfo: BedCardInfo) => {
	ElMessage.info(`入院操作 - 床位: ${bedInfo.bedNo}`);
	// TODO: 实现入院操作
};

const handleDischarge = (bedInfo: BedCardInfo) => {
	ElMessage.info(`出院操作 - 床位: ${bedInfo.bedNo}`);
	// TODO: 实现出院操作
};

const handleTransfer = (bedInfo: BedCardInfo) => {
	ElMessage.info(`转床操作 - 床位: ${bedInfo.bedNo}`);
	// TODO: 实现转床操作
};

const handleStatusChange = async (bedInfo: BedCardInfo) => {
	try {
		const { value: newStatus } = await ElMessageBox.prompt('请选择新的床位状态', '更改床位状态', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			inputType: 'select',
			inputOptions: {
				N: '空床',
				O: '占床',
				K: '污染',
				Q: '隔离',
				C: '关闭',
			},
			inputValue: bedInfo.bedStatus,
		});

		if (newStatus && newStatus !== bedInfo.bedStatus) {
			await bedCardApi.updateBedStatus(bedInfo.id, newStatus);
			ElMessage.success('床位状态更新成功');
			await handleQuery();
		}
	} catch (error) {
		console.error('更新床位状态失败:', error);
	}
};

// 工具函数
const getStatusTagType = (status: string) => {
	const typeMap: Record<string, string> = {
		N: 'success',
		O: 'primary',
		K: 'danger',
		Q: 'warning',
		C: 'info',
	};
	return typeMap[status] || 'info';
};

const getStatusText = (status: string) => {
	const textMap: Record<string, string> = {
		N: '空床',
		O: '占床',
		K: '污染',
		Q: '隔离',
		C: '关闭',
	};
	return textMap[status] || '未知';
};

const getGenderText = (sex: string) => {
	const genderMap: Record<string, string> = {
		'1': '男',
		'2': '女',
		M: '男',
		F: '女',
		Male: '男',
		Female: '女',
	};
	return genderMap[sex] || sex;
};
</script>

<style scoped lang="scss">
.bed-card-management {
	padding: 16px;
	background: #f5f7fa;
	min-height: calc(100vh - 84px);

	.filter-card {
		margin-bottom: 16px;

		.el-form {
			.el-form-item {
				margin-bottom: 16px;
			}
		}
	}

	.statistics-card {
		margin-bottom: 16px;

		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.stat-item {
			text-align: center;
			padding: 16px;
			border-radius: 8px;
			background: white;
			border: 1px solid #ebeef5;
			transition: all 0.3s ease;

			&:hover {
				transform: translateY(-2px);
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
			}

			.stat-number {
				font-size: 28px;
				font-weight: bold;
				margin-bottom: 8px;
			}

			.stat-label {
				font-size: 14px;
				color: #909399;
			}

			&.total {
				.stat-number {
					color: #606266;
				}
				border-left: 4px solid #606266;
			}

			&.occupied {
				.stat-number {
					color: #409eff;
				}
				border-left: 4px solid #409eff;
			}

			&.available {
				.stat-number {
					color: #67c23a;
				}
				border-left: 4px solid #67c23a;
			}

			&.rate {
				.stat-number {
					color: #e6a23c;
				}
				border-left: 4px solid #e6a23c;
			}
		}
	}

	.bed-grid-card {
		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.bed-container {
			min-height: 400px;
		}

		.bed-grid {
			display: grid;
			grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
			gap: 16px;
			padding: 16px 0;
		}

		.bed-list {
			.text-sm {
				font-size: 12px;
			}

			.text-gray-500 {
				color: #909399;
			}

			.text-gray-400 {
				color: #c0c4cc;
			}
		}
	}
}

// 响应式设计
@media (max-width: 1200px) {
	.bed-card-management .bed-grid-card .bed-grid {
		grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
		gap: 12px;
	}
}

@media (max-width: 768px) {
	.bed-card-management {
		padding: 8px;

		.filter-card .el-form {
			.el-form-item {
				margin-bottom: 12px;

				.el-select,
				.el-input {
					width: 100% !important;
				}
			}
		}

		.statistics-card {
			.el-row .el-col {
				margin-bottom: 12px;
			}
		}

		.bed-grid-card .bed-grid {
			grid-template-columns: 1fr;
			gap: 12px;
		}
	}
}

@media (max-width: 480px) {
	.bed-card-management {
		.filter-card .el-form {
			.el-form-item {
				display: block;

				.el-form-item__label {
					display: block;
					text-align: left;
					width: auto !important;
					margin-bottom: 4px;
				}

				.el-form-item__content {
					margin-left: 0 !important;
				}
			}
		}

		.statistics-card .stat-item {
			padding: 12px;

			.stat-number {
				font-size: 24px;
			}
		}
	}
}
</style>
