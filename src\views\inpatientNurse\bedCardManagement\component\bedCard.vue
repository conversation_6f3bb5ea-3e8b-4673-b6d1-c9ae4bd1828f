<template>
	<div class="bed-card" :class="bedCardClass">
		<!-- 床位头部信息 -->
		<div class="bed-header">
			<div class="bed-info">
				<span class="bed-no">{{ bedInfo.bedNo }}</span>
				<span class="room-no" v-if="bedInfo.roomNo">{{ bedInfo.roomNo }}</span>
			</div>
			<div class="bed-status">
				<el-tag :type="statusTagType" size="small">{{ bedStatusText }}</el-tag>
			</div>
		</div>

		<!-- 床位内容区域 -->
		<div class="bed-content">
			<!-- 有患者时显示患者信息 -->
			<div v-if="bedInfo.isOccupied && bedInfo.patientInfo" class="patient-section">
				<div class="patient-basic">
					<h4 class="patient-name">
						{{ bedInfo.patientInfo.patientName }}
						<el-tag v-if="bedInfo.isEmergency" type="danger" size="small">急</el-tag>
					</h4>
					<div class="patient-details">
						<p><span class="label">患者号:</span> {{ bedInfo.patientInfo.patientNo }}</p>
						<p><span class="label">住院号:</span> {{ bedInfo.patientInfo.inpatientNo }}</p>
						<p>
							<span class="label">基本信息:</span>
							{{ genderText }} | {{ bedInfo.patientInfo.age }}岁
						</p>
						<p><span class="label">住院天数:</span> {{ bedInfo.patientInfo.inpatientDays }}天</p>
					</div>
				</div>

				<div class="medical-info" v-if="bedInfo.patientInfo.doctorName || bedInfo.patientInfo.diagnosis">
					<p v-if="bedInfo.patientInfo.doctorName"><span class="label">主治医师:</span> {{ bedInfo.patientInfo.doctorName }}</p>
					<p v-if="bedInfo.patientInfo.diagnosis" class="diagnosis"><span class="label">诊断:</span> {{ bedInfo.patientInfo.diagnosis }}</p>
					<p v-if="bedInfo.patientInfo.nursingLevel"><span class="label">护理等级:</span> {{ bedInfo.patientInfo.nursingLevel }}</p>
				</div>
			</div>

			<!-- 空床时显示空床信息 -->
			<div v-else class="empty-bed">
				<div class="empty-icon">
					<el-icon size="32"><Bed /></el-icon>
				</div>
				<p class="empty-text">空床</p>
				<div class="bed-details">
					<p v-if="bedInfo.bedType"><span class="label">床位类型:</span> {{ bedInfo.bedType }}</p>
					<p v-if="bedInfo.bedLevelName"><span class="label">床位等级:</span> {{ bedInfo.bedLevelName }}</p>
				</div>
			</div>
		</div>

		<!-- 床位操作按钮 -->
		<div class="bed-actions" v-if="showActions">
			<el-button-group size="small">
				<el-button v-if="!bedInfo.isOccupied" type="primary" @click="handleAction('admission')" :disabled="bedInfo.bedStatus !== 'N'"> 入院 </el-button>
				<el-button v-if="bedInfo.isOccupied" type="warning" @click="handleAction('discharge')"> 出院 </el-button>
				<el-button @click="handleAction('transfer')">转床</el-button>
				<el-button @click="handleAction('status')">状态</el-button>
			</el-button-group>
		</div>

		<!-- 特殊状态标识 -->
		<div class="status-indicators">
			<el-tag v-if="bedInfo.isIsolation" type="warning" size="small" class="status-tag">隔离</el-tag>
			<el-tag v-if="bedInfo.isContaminated" type="danger" size="small" class="status-tag">污染</el-tag>
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Bed } from '@element-plus/icons-vue';
import type { BedCardInfo } from '../../../../api/inpatientNurse/bedCard';

// 组件属性
interface Props {
	bedInfo: BedCardInfo;
	showActions?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
	showActions: true,
});

// 组件事件
const emit = defineEmits<{
	admission: [bedInfo: BedCardInfo];
	discharge: [bedInfo: BedCardInfo];
	transfer: [bedInfo: BedCardInfo];
	statusChange: [bedInfo: BedCardInfo];
}>();

// 床位卡样式类
const bedCardClass = computed(() => {
	const classes = ['bed-card-base'];

	if (props.bedInfo.isOccupied) {
		classes.push('occupied');
	} else {
		classes.push('empty');
	}

	if (props.bedInfo.isIsolation) {
		classes.push('isolation');
	}

	if (props.bedInfo.isContaminated) {
		classes.push('contaminated');
	}

	if (props.bedInfo.isEmergency) {
		classes.push('emergency');
	}

	return classes;
});

// 床位状态文本
const bedStatusText = computed(() => {
	const statusMap: Record<string, string> = {
		N: '空床',
		O: '占床',
		K: '污染',
		Q: '隔离',
		C: '关闭',
	};
	return statusMap[props.bedInfo.bedStatus || 'N'] || '未知';
});

// 状态标签类型
const statusTagType = computed(() => {
	const typeMap: Record<string, string> = {
		N: 'success',
		O: 'primary',
		K: 'danger',
		Q: 'warning',
		C: 'info',
	};
	return typeMap[props.bedInfo.bedStatus || 'N'] || 'info';
});

// 性别文本
const genderText = computed(() => {
	if (!props.bedInfo.patientInfo) return '';
	const genderMap: Record<string, string> = {
		'1': '男',
		'2': '女',
		M: '男',
		F: '女',
		Male: '男',
		Female: '女',
	};
	return genderMap[props.bedInfo.patientInfo.sex] || props.bedInfo.patientInfo.sex;
});

// 处理操作事件
const handleAction = (action: string) => {
	switch (action) {
		case 'admission':
			emit('admission', props.bedInfo);
			break;
		case 'discharge':
			emit('discharge', props.bedInfo);
			break;
		case 'transfer':
			emit('transfer', props.bedInfo);
			break;
		case 'status':
			emit('statusChange', props.bedInfo);
			break;
	}
};
</script>

<style scoped lang="scss">
.bed-card {
	border: 2px solid #e4e7ed;
	border-radius: 12px;
	padding: 16px;
	background: white;
	transition: all 0.3s ease;
	position: relative;
	min-height: 200px;
	display: flex;
	flex-direction: column;

	&:hover {
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		transform: translateY(-2px);
	}

	// 占床状态
	&.occupied {
		border-color: #409eff;
		background: linear-gradient(135deg, #f0f9ff 0%, #e1f5fe 100%);
	}

	// 空床状态
	&.empty {
		border-color: #67c23a;
		background: linear-gradient(135deg, #f0f9ff 0%, #e8f5e8 100%);
	}

	// 污染状态
	&.contaminated {
		border-color: #f56c6c;
		background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
	}

	// 隔离状态
	&.isolation {
		border-color: #e6a23c;
		background: linear-gradient(135deg, #fdf6ec 0%, #fef3e2 100%);
	}

	// 急诊状态
	&.emergency {
		border-color: #f56c6c;
		border-width: 3px;
		animation: emergency-pulse 2s infinite;
	}
}

@keyframes emergency-pulse {
	0%,
	100% {
		border-color: #f56c6c;
	}
	50% {
		border-color: #ff8a8a;
	}
}

.bed-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;

	.bed-info {
		display: flex;
		align-items: center;
		gap: 8px;

		.bed-no {
			font-size: 18px;
			font-weight: bold;
			color: #303133;
		}

		.room-no {
			font-size: 14px;
			color: #909399;
			background: #f5f7fa;
			padding: 2px 6px;
			border-radius: 4px;
		}
	}
}

.bed-content {
	flex: 1;
	margin-bottom: 12px;
}

.patient-section {
	.patient-basic {
		margin-bottom: 12px;

		.patient-name {
			margin: 0 0 8px 0;
			font-size: 16px;
			font-weight: bold;
			color: #303133;
			display: flex;
			align-items: center;
			gap: 8px;
		}

		.patient-details {
			p {
				margin: 4px 0;
				font-size: 13px;
				color: #606266;
				line-height: 1.4;
			}
		}
	}

	.medical-info {
		border-top: 1px solid #ebeef5;
		padding-top: 8px;

		p {
			margin: 4px 0;
			font-size: 13px;
			color: #606266;
			line-height: 1.4;

			&.diagnosis {
				color: #409eff;
				font-weight: 500;
			}
		}
	}
}

.empty-bed {
	text-align: center;
	padding: 20px 0;

	.empty-icon {
		color: #c0c4cc;
		margin-bottom: 8px;
	}

	.empty-text {
		font-size: 16px;
		color: #909399;
		margin: 0 0 12px 0;
	}

	.bed-details {
		p {
			margin: 4px 0;
			font-size: 13px;
			color: #909399;
		}
	}
}

.label {
	color: #909399;
	font-size: 12px;
}

.bed-actions {
	margin-top: auto;

	.el-button-group {
		width: 100%;

		.el-button {
			flex: 1;
		}
	}
}

.status-indicators {
	position: absolute;
	top: 8px;
	right: 8px;
	display: flex;
	flex-direction: column;
	gap: 4px;

	.status-tag {
		font-size: 10px;
	}
}

// 响应式设计
@media (max-width: 768px) {
	.bed-card {
		padding: 12px;
		min-height: 180px;
	}

	.bed-header .bed-info .bed-no {
		font-size: 16px;
	}

	.patient-section .patient-basic .patient-name {
		font-size: 14px;
	}
}
</style>
